<!doctype html>
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
<head>
  <!-- NAME: 1 COLUMN -->
  <!--[if gte mso 15]>
  <xml>
    <o:OfficeDocumentSettings>
    <o:AllowPNG/>
    <o:PixelsPerInch>96</o:PixelsPerInch>
    </o:OfficeDocumentSettings>
  </xml>
  <![endif]-->
  <!--[if mso]>
  <style type="text/css">
  body, table, td, p, li, a {font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif !important;}
  </style>
  <![endif]-->
  <style type="text/css">
    .page-content-wrapper .logo-text{
      min-height: 0px !important;
    }
    *::selection {
      background: #348EFA;
      color: #ffffff;
    }
  </style>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
  <%= stylesheet_link_tag "mailer_design" %>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title><%= @subject %></title>
</head>
<body genuity-system-auto-generated-email style="height: 100%;margin: 0;padding: 0;width: 100%;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;background-color: #FAFAFA;">
  <center>
    <table align="center" border="0" cellpadding="0" cellspacing="0" height="100%" width="100%" id="bodyTable" style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;height: 100%;margin: 0;padding: 0;width: 100%;background-color: #FAFAFA;">
      <tr>
        <td align="center" valign="top" id="bodyCell" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;height: 100%;margin: 0;padding: 10px;width: 100%;border-top: 0;">
          <table border="0" cellpadding="0" cellspacing="0" width="100%" class="templateContainer" style="border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;border: 0;max-width: 600px !important;">
        <tr>
          <td valign="top" id="templateBody" style="mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;background-color: #FFFFFF;background-image: none;border-top: 0;border-bottom: 2px solid #EAEAEA;padding-top: 0;padding-bottom: 9px;"><table border="0" cellpadding="0" cellspacing="0" width="100%" class="mcnTextBlock" style="min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
            <tbody class="mcnTextBlockOuter">
              <tr>
                <td valign="top" class="mcnTextBlockInner" style="padding-top: 9px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;">
                  <table align="left" border="0" cellpadding="0" cellspacing="0" style="max-width: 100%;min-width: 100%;border-collapse: collapse;mso-table-lspace: 0pt;mso-table-rspace: 0pt;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;" width="100%" class="mcnTextContentContainer">
                    <tbody><tr>
                      <td valign="top" class="mcnTextContent pl-0 pr-0" style="padding-top: 0;padding-bottom: 9px;mso-line-height-rule: exactly;-ms-text-size-adjust: 100%;-webkit-text-size-adjust: 100%;word-break: break-word;color: #202020;font-family: Helvetica;font-size: 14px;line-height: 150%;text-align: left;">
                        <div class="email-template" style="overflow-x: scroll;">
                          <div style="padding: 2rem 2rem 0; width: 100%">
                            <% if email_format && email_format["enabled"] %>
                              <% if header_image && header_customization.present? %>
                                <div class="mb-2">
                                  <div
                                    class="logo-holder"
                                    style="width: <%= header_customization['width'].present? || header_customization['imageAlignment'].present? ? '100%' : '100px' %>;
                                      display: block;
                                      text-align: <%= header_customization['imageAlignment'] ? header_customization['imageAlignment'] : 'left' %>;
                                    "
                                  >
                                    <% if header_customization['width'].present? && header_customization['height'].present? %>
                                      <img src="<%= header_image %>" style="width: <%= header_customization['width'] %>px; height: <%= header_customization['height'] %>px; margin: 0 auto; display: inline-block;" alt="Genuity" />
                                    <% else %>
                                      <img src="<%= header_image %>" style="width: <%= header_customization['imageAlignment'] ? '100px' : '100%' %>; height: auto; margin: 0 auto; display: inline-block;" alt="Genuity" />
                                    <% end %>
                                  <!-- </div> -->
                                  <% if header_text&.strip.length > 0 %>
                                    <div class="logo-text" style="vertical-align: middle; min-height: 82px"">
                                      <span style="text-align: <%= header_customization['textAlignment'] %>">
                                        <%= header_text&.html_safe %>
                                      </span>
                                    </div>
                                  <% end %>
                                <!-- </div> -->
                              <% else %>
                                <div class="mb-2">
                                  <div class="logo-holder" style="width: 100px; display: inline-block;">
                                    <% if header_image %>
                                      <img src="<%= header_image %>" style="margin: 0 auto; width: 100%;" alt="Genuity" />
                                    <% end %>
                                  <!-- </div> -->
                                  <!-- <% if header_text&.strip.length > 0 %>
                                    <div class="logo-text" style="display: inline-block; vertical-align: middle; min-height: 82px;">
                                      <span>
                                        <%= header_text&.html_safe %>
                                      </span>
                                    </div>
                                  <% end %> -->
                                <!-- </div> -->
                              <% end %>
                            <!-- <% else %>
                              <% unless @hide_logo %>
                                <img src="https://nulodgic-static-assets.s3.amazonaws.com/images/logo.png" width="100" height="50" style="height: 50px; width: 100px;" alt="Genuity" />
                              <% end %>
                            <% end %> -->
                          <!-- </div>
                        </div> -->
                        <div style="padding: 0 2rem;">
