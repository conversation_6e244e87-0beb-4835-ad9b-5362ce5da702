                        </div>
  <div id="email-footer">
    <div class="pt-4">
      <div class="mb-2 mt-2 small">
        <hr style="margin-top: 0px; margin-bottom: 10px">
        <% if email_format && email_format["enabled"] && !email_format["hide_footer"] %>
          <% if footer_image %>
            <div
              style="width: <%= footer_customization&.dig('imageAlignment') ? '100%' : '70px' %>;
              display: <%= footer_customization&.dig('imageAlignment') ? 'block' : 'inline-block' %>;
              margin-bottom: 10px;
              text-align: <%= footer_customization&.dig('imageAlignment') || 'left' %>"
            >
              <% if footer_customization&.dig("width").present? && footer_customization&.dig("height").present? %>
                <img src="<%= footer_image %>" style="margin: 0 auto; width: <%= footer_customization['width'] %>px; height: <%= footer_customization['height'] %>px;" alt="Footer" />
              <% else %>
                <img 
                  src="<%= footer_image %>" 
                  style="margin: 0 auto; width: <%= footer_customization&.dig('imageAlignment') ? '100px' : '100%' %>;" 
                  alt="Genuity" 
                />
              <% end %>
            </div>
          <% end %>
          <div
            style="display: <%= footer_customization ? 'block;' : 'inline-block;'%>
              color: #6f6b6b;
              width: <%= footer_customization ? '100%' : '545px;'%>
              font-size: 16px;
            "
          >
            <% if footer_text&.strip.length > 0 || email_format["enabled"] %>
              <% if footer_customization&.dig('imageAlignment') %>
                <span style="text-align: <%= footer_customization['textAlignment'] %>"><%= footer_text&.html_safe %></span>
              <% else %>
                <span><%= footer_text&.html_safe %></span>
              <% end %>
            <% else %>
              <span>
                <small>
                  Apologies if you didn’t ask for this email. It was sent because your organization uses Genuity. You can ignore it or <a href="mailto:<EMAIL>">contact us</a> if you keep getting these messages by mistake.
                  <div class="footer-holder" style="overflow: hidden; margin: 1.5rem 0 0.8rem">
                    <div class="content-box" style="float: left;">
                      Made with 💙 in Chicago <%= Time.now.year %> Genuity    
                    </div>
                    <div class="icon-holder" style="float: right;">
                      <a style="text-decoration: none; display: inline-block; width: 24px; height: 24px;" href="https://www.facebook.com/gogenuity">
                        <img width="24" height="24" style="height: 24px; width: 24px;" src="https://nulodgic-static-assets.s3.amazonaws.com/images/facebook-logo.png" alt="Facebook Logo" />
                      </a>
                      <a style="text-decoration: none; display: inline-block; width: 24px; height: 24px; vertical-align: top;" href="https://www.linkedin.com/company/gogenuity/">
                        <img width="20" height="20" style="height: 20px; width: 20px;     margin-top: 2px;" src="https://nulodgic-static-assets.s3.amazonaws.com/images/linkedin-logo.png" alt="LinkedIn Logo" />
                      </a>
                      <a style="text-decoration: none; display: inline-block; width: 24px; height: 24px; vertical-align: top;" href="https://twitter.com/gogenuity">
                        <img width="20" height="20" style="height: 20px; width: 20px;     margin-top: 2px;" src="https://nulodgic-static-assets.s3.amazonaws.com/images/x-logo.png" alt="X Logo" />
                      </a>
                    </div>
                  </div>
                </small>
              </span>
            <% end %>
          </div>
        <% elsif !email_format || (email_format && !email_format["enabled"]) %>
          <div
            style= "display: 'inline-block';
              color: #6f6b6b;
              width: '545px';
              font-size: 16px;
            "
          >
            <span>
              <small>
                Apologies if you didn’t ask for this email. It was sent because your organization uses Genuity. You can ignore it or <a href="mailto:<EMAIL>">contact us</a> if you keep getting these messages by mistake.
                <div class="footer-holder" style="overflow: hidden; margin: 1.5rem 0 0.8rem">
                  <div class="content-box" style="float: left;">
                    Made with 💙 in Chicago <%= Time.now.year %> Genuity    
                  </div>
                  <div class="icon-holder" style="float: right;">
                    <a style="text-decoration: none; display: inline-block; width: 24px; height: 24px;" href="https://www.facebook.com/gogenuity">
                      <img width="24" height="24" style="height: 24px; width: 24px;" src="https://nulodgic-static-assets.s3.amazonaws.com/images/facebook-logo.png" alt="Facebook Logo" />
                    </a>
                    <a style="text-decoration: none; display: inline-block; width: 24px; height: 24px; vertical-align: top;" href="https://www.linkedin.com/company/gogenuity/">
                      <img width="20" height="20" style="height: 20px; width: 20px;     margin-top: 2px;" src="https://nulodgic-static-assets.s3.amazonaws.com/images/linkedin-logo.png" alt="LinkedIn Logo" />
                    </a>
                    <a style="text-decoration: none; display: inline-block; width: 24px; height: 24px; vertical-align: top;" href="https://twitter.com/gogenuity">
                      <img width="20" height="20" style="height: 20px; width: 20px;     margin-top: 2px;" src="https://nulodgic-static-assets.s3.amazonaws.com/images/x-logo.png" alt="X Logo" />
                    </a>
                  </div>
                </div>
              </small>
            </span>
          </div>
        <% end %>
      </div>
    </div>
    <% if respond_to?(:help_ticket) && help_ticket %>
      <!-- GENUITY_TICKET_GUID:<%= help_ticket.guid %> -->
    <% end %>
  </div>
</body>
</html>
