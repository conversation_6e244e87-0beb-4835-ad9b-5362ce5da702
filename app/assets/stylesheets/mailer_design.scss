/*
 * Email css reset and basic override
 */

p {
  margin: 10px 0;
  padding: 0;
  text-align: left;
}

table{
  border-collapse:collapse;
}

h1,h2,h3,h4,h5,h6{
  display:block;
  margin:0;
  padding:0;
}

.mail-body {
  img,a img {
    border:0;
    height:auto;
    outline:none;
    text-decoration:none;
  }
}

body,#bodyTable,#bodyCell{
  height:100%;
  margin:0;
  padding:0;
  width:100%;
}

#outlook a{
  padding:0;
}

img{
  -ms-interpolation-mode:bicubic;
}

table{
  mso-table-lspace:0pt;
  mso-table-rspace:0pt;
}

.ReadMsgBody{
  width:100%;
}

.ExternalClass{
  width:100%;
}

p,a,li,td,blockquote{
  mso-line-height-rule:exactly;
}

a[href^=tel],a[href^=sms]{
  color:inherit;
  cursor:default;
  text-decoration:none;
}

p,a,li,td,body,table,blockquote{
  -ms-text-size-adjust:100%;
  -webkit-text-size-adjust:100%;
}

.ExternalClass,.ExternalClass p,.ExternalClass td,.ExternalClass div,.ExternalClass span,.ExternalClass font{
  line-height:100%;
}

a[x-apple-data-detectors]{
  color:inherit !important;
  text-decoration:none !important;
  font-size:inherit !important;
  font-family:inherit !important;
  font-weight:inherit !important;
  line-height:inherit !important;
}

#bodyCell {
  padding: 10px;
}

.templateContainer{
  max-width:600px !important;
}

a.mcnButton{
  display:block;
}

.mcnImage{
  vertical-align:bottom;
}

.mcnTextContent{
  word-break:break-word;
}

.mcnTextContent img {
  height:auto !important;
}

.mcnDividerBlock{
  table-layout:fixed !important;
}

/*
@tab Page
@section Background Style
@tip Set the background color and top border for your email. You may want to choose colors that match your company's branding.
*/
body,#bodyTable{
/*@editable*/background-color:#FAFAFA;
}
/*
@tab Page
@section Background Style
@tip Set the background color and top border for your email. You may want to choose colors that match your company's branding.
*/
#bodyCell{
/*@editable*/border-top:0;
}
/*
@tab Page
@section Email Border
@tip Set the border for your email.
*/
.templateContainer{
/*@editable*/border:0;
}
/*
@tab Page
@section Heading 1
@tip Set the styling for all first-level headings in your emails. These should be the largest of your headings.
@style heading 1
*/
h1{
/*@editable*/color:#202020;
/*@editable*/font-family:Lato;
/*@editable*/font-size:26px;
/*@editable*/font-style:normal;
/*@editable*/font-weight:bold;
/*@editable*/line-height:125%;
/*@editable*/letter-spacing:normal;
/*@editable*/text-align:left;
}
/*
@tab Page
@section Heading 2
@tip Set the styling for all second-level headings in your emails.
@style heading 2
*/
h2{
/*@editable*/color:#202020;
/*@editable*/font-family:Lato;
/*@editable*/font-size:22px;
/*@editable*/font-style:normal;
/*@editable*/font-weight:bold;
/*@editable*/line-height:125%;
/*@editable*/letter-spacing:normal;
/*@editable*/text-align:left;
}
/*
@tab Page
@section Heading 3
@tip Set the styling for all third-level headings in your emails.
@style heading 3
*/
h3{
/*@editable*/color:#202020;
/*@editable*/font-family:Lato;
/*@editable*/font-size:20px;
/*@editable*/font-style:normal;
/*@editable*/font-weight:bold;
/*@editable*/line-height:125%;
/*@editable*/letter-spacing:normal;
/*@editable*/text-align:left;
}
/*
@tab Page
@section Heading 4
@tip Set the styling for all fourth-level headings in your emails. These should be the smallest of your headings.
@style heading 4
*/
h4{
/*@editable*/color:#202020;
/*@editable*/font-family:Lato;
/*@editable*/font-size:18px;
/*@editable*/font-style:normal;
/*@editable*/font-weight:bold;
/*@editable*/line-height:125%;
/*@editable*/letter-spacing:normal;
/*@editable*/text-align:left;
}
/*
@tab Preheader
@section Preheader Style
@tip Set the background color and borders for your email's preheader area.
*/
#templatePreheader{
/*@editablebackground-color:#27aae1;*/
/*@editable*/background-image:none;
/*@editable*/border-top:0;
/*@editable*/border-bottom:0;
/*@editable*/padding-top:9px;
/*@editable*/padding-bottom:9px;
}
/*
@tab Preheader
@section Preheader Text
@tip Set the styling for your email's preheader text. Choose a size and color that is easy to read.
*/
#templatePreheader .mcnTextContent,#templatePreheader .mcnTextContent p{
/*@editable*/color:#413b3b;
/*@editable*/font-family:Lato;
/*@editable*/font-size:12px;
/*@editable*/line-height:150%;
/*@editable*/text-align:left;
}
/*
@tab Preheader
@section Preheader Link
@tip Set the styling for your email's preheader links. Choose a color that helps them stand out from your text.
*/
#templatePreheader .mcnTextContent a,#templatePreheader .mcnTextContent p a{
/*@editable*/color:#494d57;
/*@editable*/font-weight:normal;
/*@editable*/text-decoration:none;
}
/*
@tab Header
@section Header Style
@tip Set the background color and borders for your email's header area.
*/
#templateHeader{
/*@editable*/background-color:#FFFFFF;
/*@editable*/background-image:none;
/*@editable*/border-top:0;
/*@editable*/border-bottom:0;
/*@editable*/padding-top:9px;
/*@editable*/padding-bottom:0;
}
/*
@tab Header
@section Header Text
@tip Set the styling for your email's header text. Choose a size and color that is easy to read.
*/
#templateHeader .mcnTextContent,#templateHeader .mcnTextContent p{
/*@editable*/color:#0A1D40;
/*@editable*/font-family:Lato;
/*@editable*/font-size:16px;
/*@editable*/line-height:150%;
/*@editable*/text-align:left;
}
/*
@tab Header
@section Header Link
@tip Set the styling for your email's header links. Choose a color that helps them stand out from your text.
*/
#templateHeader .mcnTextContent a,#templateHeader .mcnTextContent p a{
/*@editable*/color:#326CF4;
/*@editable*/font-weight:normal;
/*@editable*/text-decoration:none;
}
/*
@tab Body
@section Body Style
@tip Set the background color and borders for your email's body area.
*/
#templateBody{
/*@editable*/background-color:#FFFFFF;
/*@editable*/background-image:none;
/*@editable*/border-top:0;
/*@editable*/border-bottom:2px solid #EAEAEA;
/*@editable*/padding-top:0;
/*@editable*/padding-bottom:9px;
}
/*
@tab Body
@section Body Text
@tip Set the styling for your email's body text. Choose a size and color that is easy to read.
*/
#templateBody .mcnTextContent,#templateBody .mcnTextContent p{
/*@editable*/color:#0A1D40;
/*@editable*/font-family:Lato;
/*@editable*/font-size:16px;
/*@editable*/line-height:150%;
/*@editable*/text-align:left;
}
/*
@tab Body
@section Body Link
@tip Set the styling for your email's body links. Choose a color that helps them stand out from your text.
*/
#templateBody .mcnTextContent a,#templateBody .mcnTextContent p a{
/*@editable*/color:#326CF4;
/*@editable*/font-weight:normal;
/*@editable*/text-decoration:none;
}
/*
@tab Footer
@section Footer Style
@tip Set the background color and borders for your email's footer area.
*/
#templateFooter{
/*@editable*/
background-color:#eee;
/*@editable*/background-image:none;
/*@editable*/border-top:0;
/*@editable*/border-bottom:0;
/*@editable*/padding-top:9px;
/*@editable*/padding-bottom:9px;
}
/*
@tab Footer
@section Footer Text
@tip Set the styling for your email's footer text. Choose a size and color that is easy to read.
*/
#templateFooter .mcnTextContent,#templateFooter .mcnTextContent p{
/*@editable*/color:#ffffff;
/*@editable*/font-family:Lato;
/*@editable*/font-size:12px;
/*@editable*/line-height:150%;
/*@editable*/text-align:center;
}
/*
@tab Footer
@section Footer Link
@tip Set the styling for your email's footer links. Choose a color that helps them stand out from your text.
*/
#templateFooter .mcnTextContent a,#templateFooter .mcnTextContent p a{
/*@editable*/color:#656565;
/*@editable*/font-weight:normal;
/*@editable*/text-decoration:none;
}
@media only screen and (min-width:768px){
.templateContainer{
width:600px !important;
}

} @media only screen and (max-width: 480px){
body,table,td,p,a,li,blockquote{
-webkit-text-size-adjust:none !important;
}

} @media only screen and (max-width: 480px){
body{
width:100% !important;
min-width:100% !important;
}

} @media only screen and (max-width: 480px){
#bodyCell{
padding-top:10px !important;
}

} @media only screen and (max-width: 480px){
.mcnImage{
width:100% !important;
}

} @media only screen and (max-width: 480px){
.mcnCartContainer,.mcnCaptionTopContent,.mcnRecContentContainer,.mcnCaptionBottomContent,.mcnTextContentContainer,.mcnBoxedTextContentContainer,.mcnImageGroupContentContainer,.mcnCaptionLeftTextContentContainer,.mcnCaptionRightTextContentContainer,.mcnCaptionLeftImageContentContainer,.mcnCaptionRightImageContentContainer,.mcnImageCardLeftTextContentContainer,.mcnImageCardRightTextContentContainer{
max-width:100% !important;
width:100% !important;
}

} @media only screen and (max-width: 480px){
.mcnBoxedTextContentContainer{
min-width:100% !important;
}

} @media only screen and (max-width: 480px){
.mcnImageGroupContent{
padding:9px !important;
}

} @media only screen and (max-width: 480px){
.mcnCaptionLeftContentOuter .mcnTextContent,.mcnCaptionRightContentOuter .mcnTextContent{
padding-top:9px !important;
}

} @media only screen and (max-width: 480px){
.mcnImageCardTopImageContent,.mcnCaptionBlockInner .mcnCaptionTopContent:last-child .mcnTextContent{
padding-top:18px !important;
}

} @media only screen and (max-width: 480px){
.mcnImageCardBottomImageContent{
padding-bottom:9px !important;
}

} @media only screen and (max-width: 480px){
.mcnImageGroupBlockInner{
padding-top:0 !important;
padding-bottom:0 !important;
}

} @media only screen and (max-width: 480px){
.mcnImageGroupBlockOuter{
padding-top:9px !important;
padding-bottom:9px !important;
}

} @media only screen and (max-width: 480px){
.mcnTextContent,.mcnBoxedTextContentColumn{
padding-right:18px !important;
padding-left:18px !important;
}

} @media only screen and (max-width: 480px){
.mcnImageCardLeftImageContent,.mcnImageCardRightImageContent{
padding-right:18px !important;
padding-bottom:0 !important;
padding-left:18px !important;
}

} @media only screen and (max-width: 480px){
.mcpreview-image-uploader{
display:none !important;
width:100% !important;
}

} @media only screen and (max-width: 480px){
/*
@tab Mobile Styles
@section Heading 1
@tip Make the first-level headings larger in size for better readability on small screens.
*/
h1{
/*@editable*/font-size:22px !important;
/*@editable*/line-height:125% !important;
}

} @media only screen and (max-width: 480px){
/*
@tab Mobile Styles
@section Heading 2
@tip Make the second-level headings larger in size for better readability on small screens.
*/
h2{
/*@editable*/font-size:20px !important;
/*@editable*/line-height:125% !important;
}

} @media only screen and (max-width: 480px){
/*
@tab Mobile Styles
@section Heading 3
@tip Make the third-level headings larger in size for better readability on small screens.
*/
h3{
/*@editable*/font-size:18px !important;
/*@editable*/line-height:125% !important;
}

} @media only screen and (max-width: 480px){
/*
@tab Mobile Styles
@section Heading 4
@tip Make the fourth-level headings larger in size for better readability on small screens.
*/
h4{
/*@editable*/font-size:16px !important;
/*@editable*/line-height:150% !important;
}

} @media only screen and (max-width: 480px){
/*
@tab Mobile Styles
@section Boxed Text
@tip Make the boxed text larger in size for better readability on small screens. We recommend a font size of at least 16px.
*/
.mcnBoxedTextContentContainer .mcnTextContent,.mcnBoxedTextContentContainer .mcnTextContent p{
/*@editable*/font-size:14px !important;
/*@editable*/line-height:150% !important;
}

} @media only screen and (max-width: 480px){
/*
@tab Mobile Styles
@section Preheader Visibility
@tip Set the visibility of the email's preheader on small screens. You can hide it to save space.
*/
#templatePreheader{
/*@editable*/display:block !important;
}

} @media only screen and (max-width: 480px){
/*
@tab Mobile Styles
@section Preheader Text
@tip Make the preheader text larger in size for better readability on small screens.
*/
#templatePreheader .mcnTextContent,#templatePreheader .mcnTextContent p{
/*@editable*/font-size:14px !important;
/*@editable*/line-height:150% !important;
}

} @media only screen and (max-width: 480px){
/*
@tab Mobile Styles
@section Header Text
@tip Make the header text larger in size for better readability on small screens.
*/
#templateHeader .mcnTextContent,#templateHeader .mcnTextContent p{
/*@editable*/font-size:16px !important;
/*@editable*/line-height:150% !important;
}

} @media only screen and (max-width: 480px){
/*
@tab Mobile Styles
@section Body Text
@tip Make the body text larger in size for better readability on small screens. We recommend a font size of at least 16px.
*/
#templateBody .mcnTextContent,#templateBody .mcnTextContent p{
/*@editable*/font-size:16px !important;
/*@editable*/line-height:150% !important;
}

} @media only screen and (max-width: 480px){
/*
@tab Mobile Styles
@section Footer Text
@tip Make the footer content text larger in size for better readability on small screens.
*/
#templateFooter .mcnTextContent,#templateFooter .mcnTextContent p{
/*@editable*/font-size:14px !important;
/*@editable*/line-height:150% !important;
}

}


/*
 * Bootstrap helpers, settings, and components
 */

html {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Helvetica, Arial, sans-serif !important;
}

$directions: (
  "l": "left",
  "r": "right",
  "b": "bottom",
  "t": "top"
);
$spacing: (
  0: 0,
  1: 0.25rem,
  2: 0.5rem,
  3: 1rem,
  4: 1.5rem,
  5: 3rem
);
@each $letter, $direction in $directions {
  .m#{$letter}-0 { margin-#{$direction}: map-get($spacing, 0) !important; }
  .m#{$letter}-1 { margin-#{$direction}: map-get($spacing, 1) !important; }
  .m#{$letter}-2 { margin-#{$direction}: map-get($spacing, 2) !important; }
  .m#{$letter}-3 { margin-#{$direction}: map-get($spacing, 3) !important; }
  .m#{$letter}-4 { margin-#{$direction}: map-get($spacing, 4) !important; }
  .m#{$letter}-5 { margin-#{$direction}: map-get($spacing, 5) !important; }

  .p#{$letter}-0 { padding-#{$direction}: map-get($spacing, 0) !important; }
  .p#{$letter}-1 { padding-#{$direction}: map-get($spacing, 1) !important; }
  .p#{$letter}-2 { padding-#{$direction}: map-get($spacing, 2) !important; }
  .p#{$letter}-3 { padding-#{$direction}: map-get($spacing, 3) !important; }
  .p#{$letter}-4 { padding-#{$direction}: map-get($spacing, 4) !important; }
  .p#{$letter}-5 { padding-#{$direction}: map-get($spacing, 5) !important; }
}
@each $spacer, $value in $spacing {
  .my-#{$spacer} {
    margin-bottom: $value !important;
    margin-top: $value !important;
  }
  .mx-#{$spacer} {
    margin-left: $value !important;
    margin-right: $value !important;
  }
  .m-#{$spacer} {
    margin: $value !important;
  }

  .py-#{$spacer} {
    padding-bottom: $value !important;
    padding-top: $value !important;
  }
  .px-#{$spacer} {
    padding-left: $value !important;
    padding-right: $value !important;
  }
  .p-#{$spacer} {
    padding: $value !important;
  }
}


.rounded { border-radius: 4px !important; }
.rounded-circle { border-radius: 50% !important; }
.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }

.bg-light { background-color: #e9ecef !important; }

.small { font-size: 80% !important; }


/*
 * Custom css
 */

.email-template .help-ticket-box {
  padding: 15px;
}

.email-template .help-ticket-box div {
  display: inline;
}

.email-template .help-ticket-box.ticket-update-text h6 {
  line-height: 30px;
}

.email-template .help-ticket-box.ticket-update-text p {
  text-align: left !important;
  margin-bottom: 0px !important;
}

.email-template .contract-mail-captions h4 {
  font-weight: normal;
}

.email-template .contract-mail-captions h4 {
  font-weight: normal;
}

.email-template .contract-name_with-logo img {
  width: 2.5rem;
}

.email-template .contract-name_with-logo {
  text-align: center;
  margin: 1.5rem 0 !important;
  display: flex;
  align-items: center;
  justify-content: center;
}

.email-template .contract-name_with-logo span {
  font-size: 1.25rem;
  margin-left: 1rem;
  vertical-align: text-bottom;
}

.email-template .banner-holder {
  position: relative;
}

.email-template .banner-holder .caption {
  bottom: 12%;
}

.email-template .banner-holder.welcome-info-mail .caption {
  bottom: 25%;
}

.email-template .lifecycle-progress {
  background: linear-gradient(90deg, #28a745, #28a745 48%, #fd7e14 83%, #E14144);
  border-radius: 0.25rem;
  margin-top: 2.25rem !important;
  height: 0.5rem;
  margin: 0 auto;
  margin-bottom: 3.5rem;
  width: 90%;
  position: relative;
}

.email-template .lifecycle-progress__today-marker {
  background: #424242;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  height: 1rem;
  position: absolute;
  top: 50%;
  transform: translate(-50%,-50%);
  transition: left 0.2s ease-in-out;
  width: 1rem;
  border-radius: 50% !important;
}

.email-template .lifecycle-progress__today-text {
  font-weight: 500;
  left: 50%;
  position: absolute;
  top: 100%;
  -webkit-transform: translate(-50%, 0.25rem);
  transform: translate(-50%, 0.25rem);
  transition: left 0.2s ease-in-out;
  width: 100%;
  min-width: 58px;
  text-align: center;
  font-size: 80%;
  line-height: 17px;
}

.company-info em {
  color: #656565;
}

.company-info {
  max-width: 150px;
  text-align: left;
}

.social-logo {
  font-size:48px;
  color:grey;
  margin-left: 5px;
}

.email-template .email-banner {
  background-color: #2BAADF;
  padding: 30px 0;
  margin-bottom: 0;
  width: 100%;
}

.email-template .welcome-info-mail .email-banner {
  padding: 0px !important;
}

.email-template .caption {
  position: absolute;
  bottom: 45%;
  left: 0;
  font-weight: normal;
  font-size: 26px;
  margin-bottom: 9px;
  right: 0;
}

.email-template .caption p {
  color: #ffffff !important;
}

.email-template .caption h2 {
  color: #ffffff;
  font-weight: normal;
  font-size: 26px;
  margin-bottom: 9px;
}

.email-template .caption h4 {
  font-weight: normal;
  font-size: 16px;
  margin-bottom: 9px;
  color: #ffffff;
}

.email-template .simple-link {
  color: grey !important;
  text-decoration: none !important;
}

.email-template .banner-bottom-bar {
  margin-bottom: 15px;
}

.email-template .banner-bottom-bar img {
  width: 100%;
  vertical-align: top;
}

.email-template p {
  margin-bottom: 10px !important;
  margin-top: 0 !important;
  font-size: 14px !important;
}

.email-banner h2,
.email-banner p {
  color: #fff;
}

.email-template h1,
.email-template h2,
.email-template h4,
.email-template p {
  text-align: center !important;
}

.email-template .btn-template {
  background: #0d6efd;
  color: #fff !important;
  padding: 6px 16px;
  text-decoration: none !important;
  border-radius: 4px;
  margin: 10px 0;
  display: inline-block;
  font-weight: bold;
  font-size: 1rem;
  box-shadow:(0 2px 2px 0 rgba(0,0,0,0.14), 0 3px 1px -2px rgba(0,0,0,0.12), 0 1px 5px 0 rgba(0,0,0,0.20));
}

.email-template .btn-lg {
  padding: 25px 40px !important;
  font-size: 20px;
}

.logo-area {
  width: 100%;
  overflow: hidden;
}

.logo-area img {
  max-width: 150px;
  min-height: 150px;
  margin: 15px auto;
  display: block;
}

.email-banner .sub-text {
  color: #dedede !important;
  margin: 0 0 10px;
  font-size: 12px !important;
}

.email-banner h4 {
  text-align: center;
  color: #fff;
  font-weight: normal;
  font-size: 16px;
}

.email-template .divider {
  width: 90%;
  margin: 25px 0;
  margin-left: 5%;
}

.email-template .divider hr {
  border-color: #2BAADF;
  background: #2BAADF;
}

.email-preview-container {
  background: var(--themed-lighter);
  height: calc(100vh - 2rem);

  table {
    background-color: inherit !important;
  }
}

.email-template {
  color: #0A1D40;
  font-family: Lato, system-ui;
  p {
    font-size: 16px !important;
    color: #0A1D40;
    text-align: left !important;
  }
  .image-holder {
    margin-bottom: 3rem;
    img {
      max-width: 260px;
    }
  }
  h3,
  h2 {
    font-size: 32px;
    font-family: Lato, system-ui;
    text-align: left !important;
    color: #0A1D40;
  }
  h3 {
    margin-bottom: 1rem;
  }
  .btn-holder {
    text-align: left;
    margin: 1.5rem 0 6rem ;
    .btn.btn-primary {
      background: #326CF4;
      color: #fff !important;
      display: inline-block;
      padding: 13px 20px;
    }
  }
  .footer-holder {
    overflow: hidden;
    margin: 1.5rem 0 0.8rem;
    .content-box {
      float: left;
    }
    .icon-holder {
      float: right;
      display: flex;
      align-items: center;
      a {
        display: inline-block;
        margin-left: 5px;
      }
    }
  }
  .inner-content {
    margin-bottom: 2rem
  }

  /* Fix for pre tags and code blocks cutting off */
  pre {
    white-space: pre-wrap !important;
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    max-width: 100% !important;
    overflow-x: auto !important;
    font-family: 'Courier New', Courier, monospace !important;
    background-color: #f5f5f5 !important;
    padding: 10px !important;
    border-radius: 4px !important;
    font-size: 13px !important;
    line-height: 1.4 !important;
    margin: 10px 0 !important;
  }

  code {
    word-wrap: break-word !important;
    overflow-wrap: break-word !important;
    font-family: 'Courier New', Courier, monospace !important;
    background-color: #f5f5f5 !important;
    padding: 2px 4px !important;
    border-radius: 3px !important;
    font-size: 13px !important;
  }
}

.email-new-holder {
  color: #0A1D40;
  font-size: 16px;
  line-height: 24px;
  max-width: 600px;
  margin: 0 auto;
  padding: 2rem 0;
  a {
    color: #326CF4;
  }
  h3 {
    font-size: 32px;
    line-height: 40px;
    margin-bottom: 1.5rem;
  }
  .content-holder {
    margin-bottom: 2rem;
  }
  .list-holder {
    ul {
      padding: 0;
      margin: 0 0 2rem;
      list-style: none;
      border-bottom: 1px solid #E4E9EE;
      li {
        border-top: 1px solid #E4E9EE;
        padding: 1.5rem 0;
        .link-area-button {
          display: block;
          background: #EFF2F6;
          padding: 0.7rem;
          border-radius: 8px;
          color: #326CF4;
          margin-top: 0.8rem;
        }
      }
    }
  }
  &.onboarding {
    .btn-email {
      background-color: #326CF4;
      padding: 0.7rem 1rem;
      color: #fff;
      color: #fff;
      text-decoration: none;
      border-radius: 8px;
      display: inline-block;
    }
    .list-holder {
      ul {
        li {
          padding: 0 0 1.7rem;
        }
      }
    }
    .image-holder {
      max-width: 500px;
      margin: 2rem auto 3rem;
      img {
        width: 100%;
      }
    }
    .list-item-holder {
      display: flex;
      .img-holder {
        float: left;
        margin-right: 1rem;
      }
    }
    .list-holder {
      padding-top: 1rem;
    }
  }
  &.email-trial {
    .image-holder {
      img {
        max-width: 100%;
      }
    }
    .btn-email {
      color: #fff !important;
    }
    .list-item-holder {
      display: block;
      .img-holder {
        float: none;
        margin-bottom: 1rem;
      }
      .list-content {
        h4 {
          margin-bottom: 0.7rem;
        }
        p {
          text-align: center !important;
        }
      }
    }
    .list-holder {
      margin-bottom: 3rem;
      ul {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
        li {
          width: 47%;
          text-align: center;
        }
      }
    }
    .heading-holder {
      h3 {
        margin-bottom: 0;
        font-size: 18px;
        text-align: center !important;
      }
      p {
        text-align: center !important;
      }
    }
  }
}

.temp-spacing-bottom {
  margin: 0 0 3rem;
}

.text-lighter {
  color: #696969;
}

.large-bottom-spacing {
  margin-bottom: 4rem;
}

.email-new-holder.onboarding .list-holder ul, 
.email-new-holder.onboarding  .list-holder ul li {
  border: 0;
}

.email-trial {
  .heading-holder {
    text-align: center;
    margin: 0 0 2.5rem;
  }
}

.bth-full {
  width: 100%;
}

.footer-holder {
  overflow: hidden;
  margin: 1.5rem 0 0.8rem;
  .content-box {
    float: left;
  }
  .icon-holder {
    float: right;
    display: flex;
    align-items: center;
    a {
        display: inline-block;
        margin-left: 5px;
    }
  }
}

.trial-expired {
  margin-bottom: 3rem;
  h1 {
    font-size: 46px;
    max-width: 293px;
    text-align: left !important;
    margin-bottom: 2rem;
  }
  .btn-template {
    border-radius: 8px;
    padding: 10px 16px;
  }
  .content-holder {
    margin-bottom: 1.5rem;
  }
}
